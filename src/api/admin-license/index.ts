import { Key } from 'react';
import qs from 'qs';
import {
  AdminLicenseParams,
  CompanyExcelDownloadParams,
  DefaultPagination,
  ExpirationDateParams,
  HistoryData,
  LicenseReportParams,
  UpdateLicenseKeyParams,
  UpdateLicenseParams,
  noSearchPagination,
} from '../interface';
import request from '../request';
import urlMap from './urlMap';

const getLicenseDelLogs = (current: number) => {
  return request({
    url: urlMap.GET_LICENSE_DELETE_LOGS,
    method: 'get',
    params: {
      current,
      size: 10,
    },
  });
};

const getLicenseDelLogsDetail = (id: string) => {
  return request({
    url: urlMap.GET_LICENSE_DELETE_LOGS_DETAIL + '/' + id,
    method: 'get',
  });
};

const getLicenseEditLogs = (params: DefaultPagination) => {
  return request({
    url: urlMap.GET_LICENSE_EDIT_LOGS,
    method: 'get',
    params,
  });
};

const getLicenseEditLogsDetail = (id: string) => {
  return request({
    url: urlMap.GET_LICENSE_EDIT_LOGS_DETAIL + '/' + id,
    method: 'get',
  });
};

const getLicenseLimitationLogs = (params: noSearchPagination) => {
  return request({
    url: urlMap.GET_LICENSE_LIMITATION_LOGS,
    method: 'get',
    params,
  });
};

const getLicenseLimitationLogsDetail = (id: string) => {
  return request({
    url: urlMap.GET_LICENSE_LIMITATION_LOGS_DETAIL + '/' + id,
    method: 'get',
  });
};

const getLicenseReportingList = (params: noSearchPagination) => {
  return request({
    url: urlMap.GET_LICENSE_REPORTING,
    method: 'get',
    params,
  });
};

const getLicenseReportingDetail = (id: string) => {
  return request({
    url: urlMap.GET_LICENSE_REPORTING_DETAIL + '/' + id,
    method: 'get',
  });
};

const getLicenseReportingDownload = (params: LicenseReportParams) => {
  return request({
    url: urlMap.GET_LICENSE_REPORTING_DOWNLOAD,
    method: 'get',
    params,
    responseType: 'blob',
  });
};

const getLicenseSummaryDetailList = (params: DefaultPagination) => {
  return request({
    url: urlMap.GET_LICENSE_SUMMARY_DETAIL_LIST,
    method: 'get',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { allowDots: true });
    },
  });
};

const getLicenseSummaryDetailForm = (id: string) => {
  return request({
    url: urlMap.GET_LICENSE_SUMMARY_DETAIL_FORM,
    method: 'get',
    params: {
      id,
    },
  });
};

const putLicenseSummaryDetailForm = (data: {
  remaining: string;
  comments: string;
  id: string;
}) => {
  return request({
    url: urlMap.PUT_LICENSE_SUMMARY_DETAIL_FORM,
    method: 'put',
    data,
  });
};

const getLicenseSummaryLogs = (params: DefaultPagination) => {
  return request({
    url: urlMap.GET_LICENSE_SUMMARY_LOGS,
    method: 'get',
    params,
  });
};

const getLicenseSummaryLogsDetail = (id: string) => {
  return request({
    url: urlMap.GET_LICENSE_SUMMARY_LOGS_DETAIL + '/' + id,
    method: 'get',
  });
};

const getLicenseBatchExtendLogs = (params: DefaultPagination) => {
  return request({
    url: urlMap.GET_LICENSE_BATCH_EXTEND_LOGS,
    method: 'get',
    params,
  });
};

const getLicenseBatchExtendLogsDetail = (id: string) => {
  return request({
    url: urlMap.GET_LICENSE_BATCH_EXTEND_LOGS_DETAIL + '/' + id,
    method: 'get',
  });
};

const getLicenseBatchExtend = (params: noSearchPagination) => {
  return request({
    url: urlMap.GET_LICENSE_BATCH_EXTEND,
    method: 'get',
    params,
  });
};

const getLicenseBatchExtendDetail = (id: string) => {
  return request({
    url: urlMap.GET_LICENSE_BATCH_EXTEND_DETAIL + '/' + id,
    method: 'get',
  });
};

const getLicenseBatchExtendForm = () => {
  return request({
    url: urlMap.GET_LICENSE_BATCH_EXTEND_FORM,
    method: 'get',
  });
};

const postLicenseBatchExtendForm = (data: any) => {
  return request({
    url: urlMap.POST_LICENSE_BATCH_EXTEND,
    method: 'post',
    data,
  });
};

const getFeatureTypesList = (params: noSearchPagination) => {
  return request({
    url: urlMap.GET_FEATURE_TYPES_LIST,
    method: 'get',
    params,
  });
};

const delFeatureTypeByIds = (ids: Key[]) => {
  return request({
    url: urlMap.DEL_FEATURE_TYPES,
    method: 'delete',
    data: {
      ids,
    },
  });
};
const postFeatureTypes = (data: { name: string; type: string }) => {
  return request({
    url: urlMap.POST_FEATURE_TYPES,
    method: 'post',
    data,
  });
};

const getFeatureTypesById = (id: string) => {
  return request({
    url: urlMap.GET_FEATURE_TYPES_BY_ID + '/' + id,
    method: 'get',
  });
};

const putFeatureTypes = (data: { name: string; type: string; id: string }) => {
  return request({
    url: urlMap.PUT_FEATURE_TYPES + '/' + data.id,
    method: 'put',
    data,
  });
};

const getExpirationDate = (params: ExpirationDateParams) => {
  return request({
    url: urlMap.GET_EXPIRATION_DATE,
    method: 'get',
    params,
  });
};

const getAdminLicenseList = (params: DefaultPagination) => {
  return request({
    url: urlMap.GET_ADMIN_LICENSE_LIST,
    method: 'get',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { allowDots: true });
    },
  });
};
const delAdminLicenseByIds = (ids: Key[]) => {
  return request({
    url: urlMap.DEL_ADMIN_LICENSE,
    method: 'delete',
    data: {
      ids,
    },
  });
};
const putAdminLicenseDisableByIds = (ids: Key[]) => {
  return request({
    url: urlMap.PUT_ADMIN_LICENSE_DISABLE,
    method: 'put',
    data: {
      ids,
    },
  });
};
const putAdminLicenseEnableByIds = (ids: Key[]) => {
  return request({
    url: urlMap.PUT_ADMIN_LICENSE_ENABLE,
    method: 'put',
    data: {
      ids,
    },
  });
};
const getAdminLicenseDetail = (id: string) => {
  return request({
    url: urlMap.GET_ADMIN_LICENSE_DETAIL_BY_ID + '/' + id,
    method: 'get',
  });
};
const getAdminLicenseAddForm = () => {
  return request({
    url: urlMap.GET_ADMIN_LICENSE_ADD_FORM,
    method: 'get',
  });
};
const putAdminLicense = (data: UpdateLicenseParams) => {
  return request({
    url: urlMap.PUT_ADMIN_LICENSE,
    method: 'put',
    data,
  });
};
const postAdminLicense = (data: AdminLicenseParams) => {
  return request({
    url: urlMap.POST_ADMIN_LICENSE,
    method: 'post',
    data,
  });
};
const putAdminLicenseKey = (data: UpdateLicenseKeyParams) => {
  return request({
    url: urlMap.PUT_ADMIN_LICENSE_KEY,
    method: 'post',
    data,
  });
};
const getAdminPoolLicenseList = (params: DefaultPagination) => {
  return request({
    url: urlMap.GET_ADMIN_POOL_LICENSE_LIST,
    method: 'get',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { allowDots: true });
    },
  });
};
const delAdminPoolLicenseByIds = (ids: Key[]) => {
  return request({
    url: urlMap.DEL_ADMIN_POOL_LICENSE,
    method: 'delete',
    data: {
      ids,
    },
  });
};
const putAdminPoolLicenseRenewedByIds = (ids: Key[]) => {
  return request({
    url: urlMap.PUT_ADMIN_POOL_LICENSE_RENEWED,
    method: 'put',
    data: {
      ids,
    },
  });
};
const getAdminPoolLicenseDetail = (id: string) => {
  return request({
    url: urlMap.GET_ADMIN_POOL_LICENSE_DETAIL_BY_ID,
    method: 'get',
    params: {
      id,
    },
  });
};
const getAdminPoolLicenseAddForm = () => {
  return request({
    url: urlMap.GET_ADMIN_POOL_LICENSE_ADD_FORM,
    method: 'get',
  });
};
const putAdminPoolLicense = (data: UpdateLicenseParams) => {
  return request({
    url: urlMap.PUT_ADMIN_POOL_LICENSE,
    method: 'put',
    data,
  });
};
const postAdminPoolLicense = (data: AdminLicenseParams) => {
  return request({
    url: urlMap.POST_ADMIN_POOL_LICENSE,
    method: 'post',
    data,
  });
};
const putAdminPoolLicenseKey = (data: UpdateLicenseKeyParams) => {
  return request({
    url: urlMap.PUT_ADMIN_POOL_LICENSE_KEY,
    method: 'post',
    data,
  });
};
const getPoolLicenseExpirationDate = (params: ExpirationDateParams) => {
  return request({
    url: urlMap.GET_POOL_LICENSE_EXPIRATION_DATE,
    method: 'get',
    params,
  });
};
const getFeatureTypesRelationList = (ids: Key[]) => {
  return request({
    url: urlMap.GET_FEATURE_TYPES_RELATION_LIST,
    method: 'post',
    data: {
      ids,
    },
  });
};

const getHistoryList = (params: {
  contentTypeId: string;
  objectId: string;
}) => {
  return request({
    url: urlMap.GET_HISTORY_LIST,
    method: 'get',
    params,
  });
};

const postLicenseBatchExtendUpdate = (data: any) => {
  return request({
    url: urlMap.POST_LICENSE_BATCH_EXTEND_UPDATE + '/' + data.id,
    method: 'post',
    data,
  });
};

const getCompanyList = () => {
  return request({
    url: urlMap.GET_COMPANY_LIST,
    method: 'get',
  });
};
const postCompanyExcelUpload = (file: FormData) => {
  return request({
    url: urlMap.POST_COMPANY_EXCEL_UPLOAD,
    method: 'post',
    data: file,
  });
};

const postCompanyExcelDownload = (data: CompanyExcelDownloadParams) => {
  return request({
    url: urlMap.POST_COMPANY_EXCEL_DOWNLOAD,
    method: 'post',
    data,
    responseType: 'blob',
  });
};

const getExcelDownloadFilter = () => {
  return request({
    url: urlMap.GET_EXCEL_LIST_FILTER,
    method: 'get',
  });
};
export {
  getLicenseDelLogs,
  getLicenseEditLogs,
  getLicenseReportingList,
  getLicenseSummaryLogs,
  getLicenseDelLogsDetail,
  getLicenseEditLogsDetail,
  getLicenseLimitationLogs,
  getLicenseReportingDetail,
  getLicenseSummaryLogsDetail,
  getLicenseSummaryDetailList,
  getLicenseLimitationLogsDetail,
  getLicenseSummaryDetailForm,
  putLicenseSummaryDetailForm,
  getLicenseBatchExtendLogs,
  getLicenseBatchExtendLogsDetail,
  getLicenseBatchExtend,
  getLicenseBatchExtendDetail,
  getLicenseBatchExtendForm,
  postLicenseBatchExtendForm,
  getFeatureTypesList,
  postFeatureTypes,
  getFeatureTypesById,
  putFeatureTypes,
  delFeatureTypeByIds,
  getExpirationDate,
  getAdminLicenseList,
  delAdminLicenseByIds,
  putAdminLicenseDisableByIds,
  putAdminLicenseEnableByIds,
  getAdminLicenseDetail,
  getAdminLicenseAddForm,
  putAdminLicenseKey,
  putAdminLicense,
  postAdminLicense,
  getAdminPoolLicenseList,
  delAdminPoolLicenseByIds,
  putAdminPoolLicenseRenewedByIds,
  getAdminPoolLicenseDetail,
  getAdminPoolLicenseAddForm,
  putAdminPoolLicense,
  postAdminPoolLicense,
  putAdminPoolLicenseKey,
  getPoolLicenseExpirationDate,
  getFeatureTypesRelationList,
  getLicenseReportingDownload,
  getHistoryList,
  postLicenseBatchExtendUpdate,
  getCompanyList,
  postCompanyExcelUpload,
  postCompanyExcelDownload,
  getExcelDownloadFilter,
};
