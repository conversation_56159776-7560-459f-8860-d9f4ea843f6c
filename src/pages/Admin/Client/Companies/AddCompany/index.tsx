import React, { useMemo, useEffect, useState, Key, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import FormCom from '@/pages/Licenses/components/FormCom';
import {
  Button,
  Form,
  Input,
  message,
  Checkbox,
  Table,
  ConfigProvider,
  theme,
} from 'antd';
import '@/components/Table/table.scss';
import { PlusOutlined, CloseOutlined } from '@ant-design/icons';
import TableHeader from './components/TableHeader';
import PCTable from '@/components/Table';
import getColumns from './components/Columns';
import styles from './index.module.scss';
import { generateUUID } from '@/utils/utils';
import {
  postAddCompanyDetail,
  postEditCompanyDetail,
  getCompanyDetail,
  companyDetail,
} from '@/api/client';

const { TextArea } = Input;
type domainsRecord = {
  name: string;
  id?: number;
  key?: string;
};
type LicenseRecord = {
  assigned: number;
  expireDate: string;
  featureType: string;
  id: number;
  limit: number;
  mode: string;
  speedType: string;
};
const AddCompany = () => {
  const formItemLayout = { labelCol: { span: 6 }, wrapperCol: { span: 18 } };
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { id } = useParams();

  const [licenseData, setLicenseData] = useState<LicenseRecord[]>([]);
  const [domainsData, setDomainsData] = useState<domainsRecord[]>([
    { name: '', key: generateUUID() },
  ]);
  const [delDomainsData, setDelDomainsData] = useState<Key[]>([]);
  const [editLicenseData, setEditLicenseData] = useState<LicenseRecord[]>([]);

  const [name, setName] = useState('');
  const [loading, setLoading] = useState<boolean>(false);
  const initLimitVal = useRef<
    {
      id: number;
      value: number;
    }[]
  >([]);
  const { data, refetch } = useQuery(
    ['getCompanyDetail', { companyId: id }],
    () => getCompanyDetail({ companyId: id }),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        form.setFieldsValue(data);
        setLicenseData(data?.licenseLimitationList);
        setDomainsData([...(data?.companyDomainList || []), ...domainsData]);
        setName(data?.name);
      },
      enabled: !!id,
    }
  );

  const addDomainsData = () => {
    setDomainsData([...domainsData, { name: '', key: generateUUID() }]);
  };

  const onFinish = (values: any) => {
    console.log(values);
    console.log(editLicenseData);
    console.log(delDomainsData);
    console.log(domainsData);
    let params = {} as companyDetail & {
      deleteDomainIdList: any[];
      id: React.Key;
      licenseLimitationList: any[];
    };
    if (id) {
      params = {
        ...values,
        deleteDomainIdList: delDomainsData,
        licenseLimitationList: editLicenseData,
        companyDomainList: fileterDomainData(domainsData),
        id: +id,
      };
    } else {
      params = {
        ...values,
        companyDomainList: fileterAddDomainData(domainsData),
      };
    }
    console.log(params, +!!id);
    setLoading(true);
    const callback = +!!id ? postEditCompanyDetail : postAddCompanyDetail;
    try {
      callback(params)
        .then((res) => {
          if (res.code === 200) {
            message.success('Success');
            navigate('/admin/client/company');
          } else {
            message.error(res.message);
          }
          setLoading(false);
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };
  const fileterDomainData = (data: domainsRecord[]) => {
    const newDate = JSON.parse(JSON.stringify(data));
    const list = newDate.filter((item: domainsRecord) => item.name !== '');
    list.forEach((v: domainsRecord) => {
      v.key && delete v['key'];
    });
    return list;
  };
  const fileterAddDomainData = (data: domainsRecord[]) => {
    const newDate = JSON.parse(JSON.stringify(data));
    const list = newDate.map((v: domainsRecord) => {
      if (v.name) return v.name;
    });
    return list;
  };
  const tableCallback = (record: any, type: string, i: number, value: any) => {
    console.log(type, i, record, value);
    record[type] = value;
    const index = editLicenseData.indexOf(record);
    if (~index) {
      if (type === 'add') {
        if (value) {
          record.limit = value + initLimitVal.current[index].value;
        } else {
          record.limit = initLimitVal.current[index].value;
        }
      }
      editLicenseData.splice(index, 1, record);
    } else {
      if (type === 'add') {
        initLimitVal.current.push({
          id: record.id,
          value: record.limit,
        });
        if (value) {
          record.limit += value;
        }
      }
      editLicenseData.push(record);
    }
    setEditLicenseData([...editLicenseData]);
  };
  const columns = useMemo(() => getColumns(tableCallback, name), [name]);

  const domainsColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      render: (_: string, records: any) => {
        return (
          <Input
            defaultValue={_}
            onChange={(value) => {
              records.name = value.target.value;
            }}
          />
        );
      },
    },
    {
      title: 'Delete?',
      dataIndex: 'name',
      render: (_: string, r: domainsRecord, i: number) => {
        if (_ && r.id) {
          return (
            <Checkbox onChange={(v) => handleDomainsCheck(v, r?.id)}></Checkbox>
          );
        } else {
          return (
            <Button
              type="primary"
              shape="circle"
              size="small"
              icon={<CloseOutlined />}
              onClick={() => handleDomainsDel(i)}
            />
          );
        }
      },
    },
  ];

  const handleDomainsCheck = (value: any, id?: number) => {
    console.log(value.target.checked, id);
    if (value.target.checked && id) {
      delDomainsData.push(id);
    } else {
      if (!id) return;
      const index = delDomainsData.indexOf(id);
      if (~index) {
        delDomainsData.splice(index, 1);
      }
    }
    setDelDomainsData([...delDomainsData]);
  };

  const handleDomainsDel = (i: number) => {
    const newData = [...domainsData];
    newData.splice(i, 1);
    setDomainsData(newData);
  };

  return (
    <div className={styles.AddCompany}>
      <Form size="large" {...formItemLayout} form={form} onFinish={onFinish}>
        <FormCom pageTitle="Companies">
          <div style={{ width: 400 }}>
            <Form.Item label="name" name="name" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item label="Description" name="description">
              <Input />
            </Form.Item>
            <Form.Item
              label="Email address:"
              name="email"
              rules={[
                {
                  pattern: /^[\w.-]+@[\w.-]+\.\w+$/,
                  message: 'Please enter the correct email address',
                },
              ]}
            >
              <Input />
            </Form.Item>
          </div>
        </FormCom>
        <div className={styles.table_box}>
          <PCTable
            columns={columns}
            dataSource={licenseData}
            title={() => (
              <TableHeader style={{ margin: -8 }} title="License limitations" />
            )}
            pagination={false}
          ></PCTable>
        </div>
        <div className={styles.table_box}>
          <ConfigProvider
            theme={{
              algorithm: [theme.defaultAlgorithm],
            }}
          >
            <Table<domainsRecord>
              rowKey={(record) => record?.id || record.key || record.name}
              columns={domainsColumns}
              dataSource={domainsData}
              title={() => (
                <TableHeader style={{ margin: -8 }} title="Company domains" />
              )}
              footer={() => (
                <Button type="link" size="small" onClick={addDomainsData}>
                  <PlusOutlined /> Add another Company Domain
                </Button>
              )}
              pagination={false}
            ></Table>
          </ConfigProvider>
        </div>
        <div style={{ width: 600, margin: '0 auto' }}>
          <Form.Item
            label="Comment"
            name="comment"
            extra="Comment your changes, please"
            wrapperCol={{ span: 24 }}
            labelCol={{ span: 24 }}
            rules={[{ required: true }]}
          >
            <TextArea
              className={styles.textarea}
              autoSize={{ minRows: 10, maxRows: 10 }}
            />
          </Form.Item>
        </div>
        <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            style={{ float: 'right' }}
          >
            save
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddCompany;
